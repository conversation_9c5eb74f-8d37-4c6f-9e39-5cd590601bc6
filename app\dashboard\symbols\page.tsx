"use client"

import { useEffect, useState, useRef } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { TrendingUp, Calendar, BarChart3, Loader2 } from "lucide-react"

// TradingView Widget Component
function TradingViewWidget({
  containerId,
  scriptSrc,
  config,
  widgetName,
  height = "400px"
}: {
  containerId: string
  scriptSrc: string
  config: any
  widgetName: string
  height?: string
}) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    if (!containerRef.current) return

    const container = containerRef.current

    // Clear any existing content
    container.innerHTML = ''

    // Create TradingView widget structure
    const widgetContainer = document.createElement('div')
    widgetContainer.className = 'tradingview-widget-container'
    widgetContainer.style.height = '100%'
    widgetContainer.style.width = '100%'

    const widgetContent = document.createElement('div')
    widgetContent.className = 'tradingview-widget-container__widget'
    widgetContent.style.height = 'calc(100% - 32px)'
    widgetContent.style.width = '100%'

    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = scriptSrc
    script.async = true
    script.innerHTML = JSON.stringify(config)

    script.onload = () => {
      console.log(`✅ ${widgetName} loaded successfully`)
      setIsLoading(false)
      setHasError(false)
    }

    script.onerror = () => {
      console.error(`❌ Failed to load ${widgetName}`)
      setIsLoading(false)
      setHasError(true)
    }

    widgetContainer.appendChild(widgetContent)
    widgetContainer.appendChild(script)
    container.appendChild(widgetContainer)

    return () => {
      if (container) {
        container.innerHTML = ''
      }
    }
  }, [scriptSrc, config, widgetName])

  return (
    <div
      ref={containerRef}
      id={containerId}
      className="w-full bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
      style={{ height, minHeight: height }}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex items-center space-x-2 text-sky-400">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading {widgetName}...</span>
          </div>
        </div>
      )}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-gray-400 p-4">
            <p className="mb-2">⚠️ {widgetName} unavailable</p>
            <p className="text-sm">Please check your internet connection and refresh the page</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default function SymbolsPage() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Widget configurations
  const tickerConfig = {
    "symbols": [
      {"proName": "FX:EURUSD", "title": "EUR/USD"},
      {"proName": "FX:GBPUSD", "title": "GBP/USD"},
      {"proName": "FX:USDJPY", "title": "USD/JPY"},
      {"proName": "FX:USDCHF", "title": "USD/CHF"},
      {"proName": "FX:AUDUSD", "title": "AUD/USD"},
      {"proName": "FX:USDCAD", "title": "USD/CAD"},
      {"proName": "FX:NZDUSD", "title": "NZD/USD"},
      {"proName": "FX:EURGBP", "title": "EUR/GBP"}
    ],
    "showSymbolLogo": true,
    "colorTheme": "dark",
    "isTransparent": false,
    "displayMode": "adaptive",
    "locale": "en"
  }

  const chartConfig = {
    "width": "100%",
    "height": "500",
    "symbol": "FX:EURUSD",
    "interval": "1",
    "timezone": "Etc/UTC",
    "theme": "dark",
    "style": "1",
    "locale": "en",
    "toolbar_bg": "#f1f3f6",
    "enable_publishing": false,
    "withdateranges": true,
    "range": "1D",
    "hide_side_toolbar": false,
    "allow_symbol_change": true,
    "details": true,
    "hotlist": true,
    "calendar": true,
    "studies": ["STD;SMA"],
    "container_id": "tradingview_chart",
    "backgroundColor": "rgba(0, 42, 60, 0.9)",
    "gridColor": "rgba(135, 206, 235, 0.1)",
    "hide_top_toolbar": false,
    "hide_legend": false,
    "save_image": false,
    "watchlist": ["FX:EURUSD", "FX:GBPUSD", "FX:USDJPY", "FX:USDCHF", "FX:AUDUSD", "FX:USDCAD", "FX:NZDUSD", "FX:EURGBP"]
  }

  const calendarConfig = {
    "colorTheme": "dark",
    "isTransparent": false,
    "width": "100%",
    "height": "600",
    "locale": "en",
    "importanceFilter": "-1,0,1",
    "countryFilter": "us,gb,jp,au,ca,ch,cn,de,fr,it,nz,kr,in,br,mx,za",
    "currencyFilter": "USD,EUR,GBP,JPY,AUD,CAD,CHF,NZD,CNY",
    "dateFormat": "dd/MM/yyyy"
  }

  const crossRatesConfig = {
    "width": "100%",
    "height": "400",
    "currencies": ["EUR", "USD", "JPY", "GBP", "CHF", "AUD", "CAD", "NZD", "CNY", "SEK"],
    "isTransparent": false,
    "colorTheme": "dark",
    "locale": "en",
    "backgroundColor": "rgba(0, 42, 60, 0.9)"
  }

  // Prevent hydration issues by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2 text-sky-400">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="text-lg">Loading Market Analysis...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-sky-500/20 to-sky-400/20 rounded-lg border border-sky-500/30">
            <TrendingUp className="h-6 w-6 text-sky-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Market Analysis</h1>
            <p className="text-gray-400">Real-time symbols, charts, and economic calendar</p>
          </div>
        </div>

        {/* Real-time Ticker */}
        <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20 mb-6">
          <CardContent className="p-4">
            <div 
              id="tradingview-ticker-widget" 
              className="w-full min-h-[80px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10"
            />
          </CardContent>
        </Card>

        <Tabs defaultValue="symbols" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-[#002a3c]/50 border border-sky-500/20">
            <TabsTrigger 
              value="symbols" 
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Live Charts
            </TabsTrigger>
            <TabsTrigger 
              value="calendar" 
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Economic Calendar
            </TabsTrigger>
            <TabsTrigger 
              value="cross-rates" 
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Cross Rates
            </TabsTrigger>
          </TabsList>

          <TabsContent value="symbols" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-sky-400" />
                  Real-time Trading Charts
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Advanced live charts with real-time price data and technical analysis tools
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  id="tradingview-symbol-widget" 
                  className="w-full min-h-[500px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-sky-400">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span>Loading TradingView widgets...</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-sky-400" />
                  Economic Calendar
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Important economic events and market-moving news
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  id="tradingview-calendar-widget" 
                  className="w-full min-h-[600px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-sky-400">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span>Loading economic calendar...</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cross-rates" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-sky-400" />
                  Forex Cross Rates
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Real-time cross rates for major currencies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  id="tradingview-cross-rates-widget" 
                  className="w-full min-h-[400px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-sky-400">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span>Loading cross rates...</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
