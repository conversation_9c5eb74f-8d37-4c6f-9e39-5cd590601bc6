"use client"

import { motion } from "framer-motion"
import { Accordion, Accordion<PERSON>ontent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { HelpCircle } from "lucide-react"

export default function FaqSection() {
  const faqs = [
    {
      question: "What is a trading challenge?",
      answer:
        "A trading challenge is an evaluation process that tests your trading skills and risk management. After passing the challenge, you gain access to a funded account where you can earn a percentage of the profits you generate without risking your own capital.",
    },
    {
      question: "How does the evaluation process work?",
      answer:
        "Our evaluation process consists of different options. For Step 1 and Step 2, you need to reach profit targets while adhering to our risk management rules. For HFT Neo, the process is optimized for high-frequency traders. With Instant accounts, you can skip the evaluation entirely and start trading with our capital immediately.",
    },
    {
      question: "What trading platforms do you support?",
      answer:
        "We support MetaTrader 4 (MT4), MetaTrader 5 (MT5), and cTrader platforms. You can choose your preferred platform during the registration process.",
    },
    {
      question: "How and when do I receive my profit payouts?",
      answer:
        "Profit payouts are processed on a bi-weekly basis. Once you request a withdrawal, the funds will be sent to your designated payment method within 24 hours. You'll receive your agreed profit split percentage.",
    },
    {
      question: "Are there any restrictions on trading styles or strategies?",
      answer:
        "We allow most trading styles and strategies, including scalping, day trading, swing trading, and position trading. However, we prohibit certain high-risk practices such as martingale strategies, excessive risk per trade, and arbitrage.",
    },
    {
      question: "Can I trade from any country?",
      answer:
        "Yes, our program is available worldwide. However, due to regulatory restrictions, traders from certain countries may need to provide additional verification.",
    },
  ]

  return (
    <section id="faq" className="py-24 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative">
      {/* Subtle background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-64 h-64 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-4">
            <HelpCircle className="h-6 w-6 text-sky-400 mr-2" />
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm">Support</span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Frequently Asked{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Questions
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Find answers to common questions about our trading challenges and funding process
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <AccordionItem
                  value={`item-${index}`}
                  className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 rounded-lg px-6 hover:border-sky-400/40 transition-all duration-300"
                >
                  <AccordionTrigger className="text-white hover:text-sky-400 text-left py-6 text-lg font-semibold">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-300 pb-6 leading-relaxed">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </motion.div>
            ))}
          </Accordion>
        </motion.div>

        {/* Contact support section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-8 py-6 max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-white mb-2">Still have questions?</h3>
            <p className="text-gray-300 mb-4">Our support team is here to help you 24/7</p>
            <div className="flex items-center justify-center space-x-4">
              <span className="text-sky-400 font-medium">Contact Support</span>
              <span className="text-gray-400">•</span>
              <span className="text-sky-400 font-medium">Live Chat</span>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
